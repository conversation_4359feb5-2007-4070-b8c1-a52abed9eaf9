// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminProject is the golang structure of table hg_admin_project for DAO operations like Where/Data.
type AdminProject struct {
	g.Meta      `orm:"table:hg_admin_project, do:true"`
	Id          interface{} // 项目id
	ProjectName interface{} // 项目名称
	IndexImage  interface{} // 预览图片url
	Content     interface{} // 项目数据
	Status      interface{} // 项目状态,-1: 未发布'1: 已发布
	Remarks     interface{} // 项目备注
	CreatedBy   interface{} // 创建人
	UpdatedAt   *gtime.Time // 更新时间
	CreatedAt   *gtime.Time // 创建时间
	DeletedAt   *gtime.Time // 删除时间
}
