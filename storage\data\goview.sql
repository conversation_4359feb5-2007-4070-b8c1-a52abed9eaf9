-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2024-03-04 11:50:47
-- 服务器版本： 5.7.40-log
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `goview`
--

-- --------------------------------------------------------

--
-- 表的结构 `hg_admin_attachment`
--

CREATE TABLE `hg_admin_attachment` (
  `id` bigint(20) NOT NULL COMMENT '文件ID',
  `app_id` varchar(64) NOT NULL COMMENT '应用ID',
  `member_id` bigint(20) DEFAULT '0' COMMENT '管理员ID',
  `cate_id` bigint(20) UNSIGNED DEFAULT '0' COMMENT '上传分类',
  `drive` varchar(64) DEFAULT NULL COMMENT '上传驱动',
  `name` varchar(1000) DEFAULT NULL COMMENT '文件原始名',
  `kind` varchar(16) DEFAULT NULL COMMENT '上传类型',
  `mime_type` varchar(128) NOT NULL DEFAULT '' COMMENT '扩展类型',
  `naive_type` varchar(32) NOT NULL COMMENT 'NaiveUI类型',
  `path` varchar(1000) DEFAULT NULL COMMENT '本地路径',
  `file_url` varchar(1000) DEFAULT NULL COMMENT 'url',
  `size` bigint(20) DEFAULT '0' COMMENT '文件大小',
  `ext` varchar(50) DEFAULT NULL COMMENT '扩展名',
  `md5` varchar(32) DEFAULT NULL COMMENT 'md5校验码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='附件管理' ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- 表的结构 `hg_admin_member`
--

CREATE TABLE `hg_admin_member` (
  `id` bigint(20) NOT NULL COMMENT '管理员ID',
  `nickname` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '昵称',
  `username` varchar(20) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '帐号',
  `password_hash` char(32) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '密码',
  `salt` char(16) CHARACTER SET utf8mb4 NOT NULL COMMENT '密码盐',
  `avatar` char(150) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '头像',
  `last_active_at` datetime DEFAULT NULL COMMENT '最后活跃时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表' ROW_FORMAT=COMPACT;

--
-- 转存表中的数据 `hg_admin_member`
--

INSERT INTO `hg_admin_member` (`id`, `nickname`, `username`, `password_hash`, `salt`, `avatar`, `last_active_at`, `remark`, `status`, `created_at`, `updated_at`) VALUES
(1, '孟帅', 'admin', 'ce72f929f1acdd4f21a5f8337e6a9aed', '6541561', 'http://bufanyun.cn-bj.ufileos.com/hotgo/attachment/2023-02-09/cqdq8er9nfkchdopav.png', '2024-03-01 17:19:34', NULL, 1, '2024-03-01 17:09:45', '2024-03-01 17:19:34');

-- --------------------------------------------------------

--
-- 表的结构 `hg_admin_project`
--

CREATE TABLE `hg_admin_project` (
  `id` bigint(20) NOT NULL COMMENT '项目id',
  `project_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '项目名称',
  `index_image` longtext CHARACTER SET utf8mb4 COMMENT '预览图片url',
  `content` longtext CHARACTER SET utf8mb4 COMMENT '项目数据',
  `status` tinyint(1) NOT NULL COMMENT '项目状态,-1: 未发布''1: 已发布',
  `remarks` longtext CHARACTER SET utf8mb4 COMMENT '项目备注',
  `created_by` bigint(20) NOT NULL COMMENT '创建人',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='项目' ROW_FORMAT=COMPACT;

--
-- 转存表中的数据 `hg_admin_project`
--

INSERT INTO `hg_admin_project` (`id`, `project_name`, `index_image`, `content`, `status`, `remarks`, `created_by`, `updated_at`, `created_at`, `deleted_at`) VALUES
(202403040972038, '测试项目23', 'http://bufanyun.cn-bj.ufileos.com/haokav2/attachment/images/2024-03-04/czknnv7p1xi6gsruzi.png', '{\n  \"editCanvasConfig\": {\n    \"projectName\": \"测试项目23\",\n    \"width\": 1920,\n    \"height\": 1080,\n    \"filterShow\": false,\n    \"hueRotate\": 0,\n    \"saturate\": 1,\n    \"contrast\": 1,\n    \"brightness\": 1,\n    \"opacity\": 1,\n    \"rotateZ\": 0,\n    \"rotateX\": 0,\n    \"rotateY\": 0,\n    \"skewX\": 0,\n    \"skewY\": 0,\n    \"blendMode\": \"normal\",\n    \"background\": null,\n    \"backgroundImage\": \"http://bufanyun.cn-bj.ufileos.com/haokav2/attachment/images/2024-03-04/czknnv7p1xi6gsruzi.png\",\n    \"selectColor\": false,\n    \"chartThemeColor\": \"macarons\",\n    \"chartCustomThemeColorInfo\": null,\n    \"chartThemeSetting\": {\n      \"title\": {\n        \"show\": true,\n        \"textStyle\": {\n          \"color\": \"#BFBFBF\",\n          \"fontSize\": 18\n        },\n        \"subtextStyle\": {\n          \"color\": \"#A2A2A2\",\n          \"fontSize\": 14\n        }\n      },\n      \"xAxis\": {\n        \"show\": true,\n        \"name\": \"\",\n        \"nameGap\": 15,\n        \"nameTextStyle\": {\n          \"color\": \"#B9B8CE\",\n          \"fontSize\": 12\n        },\n        \"inverse\": false,\n        \"axisLabel\": {\n          \"show\": true,\n          \"fontSize\": 12,\n          \"color\": \"#B9B8CE\",\n          \"rotate\": 0\n        },\n        \"position\": \"bottom\",\n        \"axisLine\": {\n          \"show\": true,\n          \"lineStyle\": {\n            \"color\": \"#B9B8CE\",\n            \"width\": 1\n          },\n          \"onZero\": true\n        },\n        \"axisTick\": {\n          \"show\": true,\n          \"length\": 5\n        },\n        \"splitLine\": {\n          \"show\": false,\n          \"lineStyle\": {\n            \"color\": \"#484753\",\n            \"width\": 1,\n            \"type\": \"solid\"\n          }\n        }\n      },\n      \"yAxis\": {\n        \"show\": true,\n        \"name\": \"\",\n        \"nameGap\": 15,\n        \"nameTextStyle\": {\n          \"color\": \"#B9B8CE\",\n          \"fontSize\": 12\n        },\n        \"inverse\": false,\n        \"axisLabel\": {\n          \"show\": true,\n          \"fontSize\": 12,\n          \"color\": \"#B9B8CE\",\n          \"rotate\": 0\n        },\n        \"position\": \"left\",\n        \"axisLine\": {\n          \"show\": true,\n          \"lineStyle\": {\n            \"color\": \"#B9B8CE\",\n            \"width\": 1\n          },\n          \"onZero\": true\n        },\n        \"axisTick\": {\n          \"show\": true,\n          \"length\": 5\n        },\n        \"splitLine\": {\n          \"show\": true,\n          \"lineStyle\": {\n            \"color\": \"#484753\",\n            \"width\": 1,\n            \"type\": \"solid\"\n          }\n        }\n      },\n      \"legend\": {\n        \"show\": true,\n        \"type\": \"scroll\",\n        \"x\": \"center\",\n        \"y\": \"top\",\n        \"icon\": \"circle\",\n        \"orient\": \"horizontal\",\n        \"textStyle\": {\n          \"color\": \"#B9B8CE\",\n          \"fontSize\": 18\n        },\n        \"itemHeight\": 15,\n        \"itemWidth\": 15,\n        \"pageTextStyle\": {\n          \"color\": \"#B9B8CE\"\n        }\n      },\n      \"grid\": {\n        \"show\": false,\n        \"left\": \"10%\",\n        \"top\": \"60\",\n        \"right\": \"10%\",\n        \"bottom\": \"60\"\n      },\n      \"dataset\": null,\n      \"renderer\": \"svg\"\n    },\n    \"previewScaleType\": \"fit\"\n  },\n  \"componentList\": [\n    {\n      \"id\": \"17a3z1foapcw00\",\n      \"isGroup\": false,\n      \"attr\": {\n        \"x\": 11,\n        \"y\": 594,\n        \"w\": 617,\n        \"h\": 464,\n        \"offsetX\": 0,\n        \"offsetY\": 0,\n        \"zIndex\": -1\n      },\n      \"styles\": {\n        \"filterShow\": false,\n        \"hueRotate\": 0,\n        \"saturate\": 1,\n        \"contrast\": 1,\n        \"brightness\": 1,\n        \"opacity\": 1,\n        \"rotateZ\": 0,\n        \"rotateX\": 0,\n        \"rotateY\": 0,\n        \"skewX\": 0,\n        \"skewY\": 0,\n        \"blendMode\": \"normal\",\n        \"animations\": []\n      },\n      \"preview\": {\n        \"overFlowHidden\": false\n      },\n      \"status\": {\n        \"lock\": false,\n        \"hide\": false\n      },\n      \"request\": {\n        \"requestDataType\": 0,\n        \"requestHttpType\": \"get\",\n        \"requestUrl\": \"\",\n        \"requestInterval\": null,\n        \"requestIntervalUnit\": \"second\",\n        \"requestContentType\": 0,\n        \"requestParamsBodyType\": \"none\",\n        \"requestSQLContent\": {\n          \"sql\": \"select * from  where\"\n        },\n        \"requestParams\": {\n          \"Body\": {\n            \"form-data\": {},\n            \"x-www-form-urlencoded\": {},\n            \"json\": \"\",\n            \"xml\": \"\"\n          },\n          \"Header\": {},\n          \"Params\": {}\n        }\n      },\n      \"filter\": null,\n      \"events\": {\n        \"baseEvent\": {\n          \"click\": null,\n          \"dblclick\": null,\n          \"mouseenter\": null,\n          \"mouseleave\": null\n        },\n        \"advancedEvents\": {\n          \"vnodeMounted\": null,\n          \"vnodeBeforeMount\": null\n        },\n        \"interactEvents\": []\n      },\n      \"key\": \"BarCrossrange\",\n      \"chartConfig\": {\n        \"key\": \"BarCrossrange\",\n        \"chartKey\": \"VBarCrossrange\",\n        \"conKey\": \"VCBarCrossrange\",\n        \"title\": \"横向柱状图\",\n        \"category\": \"Bars\",\n        \"categoryName\": \"柱状图\",\n        \"package\": \"Charts\",\n        \"chartFrame\": \"echarts\",\n        \"image\": \"bar_y.png\"\n      },\n      \"option\": {\n        \"legend\": {\n          \"show\": true,\n          \"type\": \"scroll\",\n          \"x\": \"center\",\n          \"y\": \"top\",\n          \"icon\": \"circle\",\n          \"orient\": \"horizontal\",\n          \"textStyle\": {\n            \"color\": \"#B9B8CE\",\n            \"fontSize\": 18\n          },\n          \"itemHeight\": 15,\n          \"itemWidth\": 15,\n          \"pageTextStyle\": {\n            \"color\": \"#B9B8CE\"\n          }\n        },\n        \"xAxis\": {\n          \"show\": true,\n          \"name\": \"\",\n          \"nameGap\": 15,\n          \"nameTextStyle\": {\n            \"color\": \"#B9B8CE\",\n            \"fontSize\": 12\n          },\n          \"inverse\": false,\n          \"axisLabel\": {\n            \"show\": true,\n            \"fontSize\": 12,\n            \"color\": \"#B9B8CE\",\n            \"rotate\": 0\n          },\n          \"position\": \"bottom\",\n          \"axisLine\": {\n            \"show\": true,\n            \"lineStyle\": {\n              \"color\": \"#B9B8CE\",\n              \"width\": 1\n            },\n            \"onZero\": true\n          },\n          \"axisTick\": {\n            \"show\": true,\n            \"length\": 5\n          },\n          \"splitLine\": {\n            \"show\": false,\n            \"lineStyle\": {\n              \"color\": \"#484753\",\n              \"width\": 1,\n              \"type\": \"solid\"\n            }\n          },\n          \"type\": \"value\"\n        },\n        \"yAxis\": {\n          \"show\": true,\n          \"name\": \"\",\n          \"nameGap\": 15,\n          \"nameTextStyle\": {\n            \"color\": \"#B9B8CE\",\n            \"fontSize\": 12\n          },\n          \"inverse\": false,\n          \"axisLabel\": {\n            \"show\": true,\n            \"fontSize\": 12,\n            \"color\": \"#B9B8CE\",\n            \"rotate\": 0\n          },\n          \"position\": \"left\",\n          \"axisLine\": {\n            \"show\": true,\n            \"lineStyle\": {\n              \"color\": \"#B9B8CE\",\n              \"width\": 1\n            },\n            \"onZero\": true\n          },\n          \"axisTick\": {\n            \"show\": true,\n            \"length\": 5\n          },\n          \"splitLine\": {\n            \"show\": true,\n            \"lineStyle\": {\n              \"color\": \"#484753\",\n              \"width\": 1,\n              \"type\": \"solid\"\n            }\n          },\n          \"type\": \"category\"\n        },\n        \"grid\": {\n          \"show\": false,\n          \"left\": \"10%\",\n          \"top\": \"60\",\n          \"right\": \"10%\",\n          \"bottom\": \"60\"\n        },\n        \"tooltip\": {\n          \"show\": true,\n          \"trigger\": \"axis\",\n          \"axisPointer\": {\n            \"show\": true,\n            \"type\": \"shadow\"\n          }\n        },\n        \"dataset\": {\n          \"dimensions\": [\n            \"product\",\n            \"data1\",\n            \"data2\"\n          ],\n          \"source\": [\n            {\n              \"product\": \"Mon\",\n              \"data1\": 120,\n              \"data2\": 130\n            },\n            {\n              \"product\": \"Tue\",\n              \"data1\": 200,\n              \"data2\": 130\n            },\n            {\n              \"product\": \"Wed\",\n              \"data1\": 150,\n              \"data2\": 312\n            },\n            {\n              \"product\": \"Thu\",\n              \"data1\": 80,\n              \"data2\": 268\n            },\n            {\n              \"product\": \"Fri\",\n              \"data1\": 70,\n              \"data2\": 155\n            },\n            {\n              \"product\": \"Sat\",\n              \"data1\": 110,\n              \"data2\": 117\n            },\n            {\n              \"product\": \"Sun\",\n              \"data1\": 130,\n              \"data2\": 160\n            }\n          ]\n        },\n        \"series\": [\n          {\n            \"type\": \"bar\",\n            \"barWidth\": null,\n            \"label\": {\n              \"show\": true,\n              \"position\": \"right\",\n              \"color\": \"#fff\",\n              \"fontSize\": 12\n            },\n            \"itemStyle\": {\n              \"color\": null,\n              \"borderRadius\": 0\n            }\n          },\n          {\n            \"type\": \"bar\",\n            \"barWidth\": null,\n            \"label\": {\n              \"show\": true,\n              \"position\": \"right\",\n              \"color\": \"#fff\",\n              \"fontSize\": 12\n            },\n            \"itemStyle\": {\n              \"color\": null,\n              \"borderRadius\": 0\n            }\n          }\n        ],\n        \"backgroundColor\": \"rgba(0,0,0,0)\"\n      }\n    },\n    {\n      \"id\": \"2kypivesa7o000\",\n      \"isGroup\": false,\n      \"attr\": {\n        \"x\": 1177,\n        \"y\": 425,\n        \"w\": 514,\n        \"h\": 141,\n        \"offsetX\": 0,\n        \"offsetY\": 0,\n        \"zIndex\": -1\n      },\n      \"styles\": {\n        \"filterShow\": false,\n        \"hueRotate\": 0,\n        \"saturate\": 1,\n        \"contrast\": 1,\n        \"brightness\": 1,\n        \"opacity\": 1,\n        \"rotateZ\": 0,\n        \"rotateX\": 0,\n        \"rotateY\": 0,\n        \"skewX\": 0,\n        \"skewY\": 0,\n        \"blendMode\": \"normal\",\n        \"animations\": []\n      },\n      \"preview\": {\n        \"overFlowHidden\": false\n      },\n      \"status\": {\n        \"lock\": false,\n        \"hide\": false\n      },\n      \"request\": {\n        \"requestDataType\": 2,\n        \"requestHttpType\": \"patch\",\n        \"requestUrl\": \"/sys/testData\",\n        \"requestInterval\": 5,\n        \"requestIntervalUnit\": \"second\",\n        \"requestContentType\": 0,\n        \"requestParamsBodyType\": \"none\",\n        \"requestSQLContent\": {\n          \"sql\": \"select * from  where\"\n        },\n        \"requestParams\": {\n          \"Body\": {\n            \"form-data\": {},\n            \"x-www-form-urlencoded\": {},\n            \"json\": \"\",\n            \"xml\": \"\"\n          },\n          \"Header\": {},\n          \"Params\": {}\n        },\n        \"requestDataPondId\": \"5mb1sm98wag000\"\n      },\n      \"filter\": \"return data.deviceInfo.deviceTime\",\n      \"events\": {\n        \"baseEvent\": {\n          \"click\": null,\n          \"dblclick\": null,\n          \"mouseenter\": null,\n          \"mouseleave\": null\n        },\n        \"advancedEvents\": {\n          \"vnodeMounted\": null,\n          \"vnodeBeforeMount\": null\n        },\n        \"interactEvents\": []\n      },\n      \"key\": \"TextCommon\",\n      \"chartConfig\": {\n        \"key\": \"TextCommon\",\n        \"chartKey\": \"VTextCommon\",\n        \"conKey\": \"VCTextCommon\",\n        \"title\": \"时间\",\n        \"category\": \"Texts\",\n        \"categoryName\": \"文本\",\n        \"package\": \"Informations\",\n        \"chartFrame\": \"common\",\n        \"image\": \"text_static.png\"\n      },\n      \"option\": {\n        \"link\": \"\",\n        \"linkHead\": \"http://\",\n        \"dataset\": \"2024-03-04 11:44:33\",\n        \"fontSize\": 36,\n        \"fontColor\": \"#0E0101FF\",\n        \"paddingX\": 10,\n        \"paddingY\": 10,\n        \"textAlign\": \"start\",\n        \"fontWeight\": \"bold\",\n        \"borderWidth\": 0,\n        \"borderColor\": \"#ffffff\",\n        \"borderRadius\": 5,\n        \"letterSpacing\": 5,\n        \"writingMode\": \"horizontal-tb\",\n        \"backgroundColor\": \"#00000000\"\n      }\n    },\n    {\n      \"id\": \"6ysezyw5ub400\",\n      \"isGroup\": false,\n      \"attr\": {\n        \"x\": 1177,\n        \"y\": 378,\n        \"w\": 442,\n        \"h\": 76,\n        \"offsetX\": 0,\n        \"offsetY\": 0,\n        \"zIndex\": -1\n      },\n      \"styles\": {\n        \"filterShow\": false,\n        \"hueRotate\": 0,\n        \"saturate\": 1,\n        \"contrast\": 1,\n        \"brightness\": 1,\n        \"opacity\": 1,\n        \"rotateZ\": 0,\n        \"rotateX\": 0,\n        \"rotateY\": 0,\n        \"skewX\": 0,\n        \"skewY\": 0,\n        \"blendMode\": \"normal\",\n        \"animations\": []\n      },\n      \"preview\": {\n        \"overFlowHidden\": false\n      },\n      \"status\": {\n        \"lock\": false,\n        \"hide\": false\n      },\n      \"request\": {\n        \"requestDataType\": 2,\n        \"requestHttpType\": \"patch\",\n        \"requestUrl\": \"/sys/testData\",\n        \"requestInterval\": 5,\n        \"requestIntervalUnit\": \"second\",\n        \"requestContentType\": 0,\n        \"requestParamsBodyType\": \"none\",\n        \"requestSQLContent\": {\n          \"sql\": \"select * from  where\"\n        },\n        \"requestParams\": {\n          \"Body\": {\n            \"form-data\": {},\n            \"x-www-form-urlencoded\": {},\n            \"json\": \"\",\n            \"xml\": \"\"\n          },\n          \"Header\": {},\n          \"Params\": {}\n        },\n        \"requestDataPondId\": \"5mb1sm98wag000\"\n      },\n      \"filter\": \"return \'当前温度：\'+data.deviceData.temperature\",\n      \"events\": {\n        \"baseEvent\": {\n          \"click\": null,\n          \"dblclick\": null,\n          \"mouseenter\": null,\n          \"mouseleave\": null\n        },\n        \"advancedEvents\": {\n          \"vnodeMounted\": null,\n          \"vnodeBeforeMount\": null\n        },\n        \"interactEvents\": []\n      },\n      \"key\": \"TextCommon\",\n      \"chartConfig\": {\n        \"key\": \"TextCommon\",\n        \"chartKey\": \"VTextCommon\",\n        \"conKey\": \"VCTextCommon\",\n        \"title\": \"设备温度\",\n        \"category\": \"Texts\",\n        \"categoryName\": \"文本\",\n        \"package\": \"Informations\",\n        \"chartFrame\": \"common\",\n        \"image\": \"text_static.png\"\n      },\n      \"option\": {\n        \"link\": \"\",\n        \"linkHead\": \"http://\",\n        \"dataset\": \"当前温度：98.28°C\",\n        \"fontSize\": 36,\n        \"fontColor\": \"#0D0202FF\",\n        \"paddingX\": 10,\n        \"paddingY\": 10,\n        \"textAlign\": \"start\",\n        \"fontWeight\": \"bold\",\n        \"borderWidth\": 0,\n        \"borderColor\": \"#ffffff\",\n        \"borderRadius\": 5,\n        \"letterSpacing\": 5,\n        \"writingMode\": \"horizontal-tb\",\n        \"backgroundColor\": \"#00000000\"\n      }\n    },\n    {\n      \"id\": \"4tzqumjxyia000\",\n      \"isGroup\": false,\n      \"attr\": {\n        \"x\": 1177,\n        \"y\": 278,\n        \"w\": 481,\n        \"h\": 131,\n        \"offsetX\": 0,\n        \"offsetY\": 0,\n        \"zIndex\": -1\n      },\n      \"styles\": {\n        \"filterShow\": false,\n        \"hueRotate\": 0,\n        \"saturate\": 1,\n        \"contrast\": 1,\n        \"brightness\": 1,\n        \"opacity\": 1,\n        \"rotateZ\": 0,\n        \"rotateX\": 0,\n        \"rotateY\": 0,\n        \"skewX\": 0,\n        \"skewY\": 0,\n        \"blendMode\": \"normal\",\n        \"animations\": []\n      },\n      \"preview\": {\n        \"overFlowHidden\": false\n      },\n      \"status\": {\n        \"lock\": false,\n        \"hide\": false\n      },\n      \"request\": {\n        \"requestDataType\": 2,\n        \"requestHttpType\": \"post\",\n        \"requestUrl\": \"/sys/testData\",\n        \"requestInterval\": null,\n        \"requestIntervalUnit\": \"second\",\n        \"requestContentType\": 0,\n        \"requestParamsBodyType\": \"none\",\n        \"requestSQLContent\": {\n          \"sql\": \"select * from test  where 1\"\n        },\n        \"requestParams\": {\n          \"Body\": {\n            \"form-data\": {},\n            \"x-www-form-urlencoded\": {},\n            \"json\": \"\",\n            \"xml\": \"\"\n          },\n          \"Header\": {},\n          \"Params\": {}\n        }\n      },\n      \"filter\": \"console.log(\'data:\'+JSON.stringify(data))\\r\\nreturn data.deviceInfo.deviceId\",\n      \"events\": {\n        \"baseEvent\": {\n          \"click\": null,\n          \"dblclick\": null,\n          \"mouseenter\": null,\n          \"mouseleave\": null\n        },\n        \"advancedEvents\": {\n          \"vnodeMounted\": null,\n          \"vnodeBeforeMount\": null\n        },\n        \"interactEvents\": []\n      },\n      \"key\": \"TextCommon\",\n      \"chartConfig\": {\n        \"key\": \"TextCommon\",\n        \"chartKey\": \"VTextCommon\",\n        \"conKey\": \"VCTextCommon\",\n        \"title\": \"设备名称\",\n        \"category\": \"Texts\",\n        \"categoryName\": \"文本\",\n        \"package\": \"Informations\",\n        \"chartFrame\": \"common\",\n        \"image\": \"text_static.png\"\n      },\n      \"option\": {\n        \"link\": \"\",\n        \"linkHead\": \"http://\",\n        \"dataset\": \"IoTDevice001\",\n        \"fontSize\": 36,\n        \"fontColor\": \"#0D0000FF\",\n        \"paddingX\": 10,\n        \"paddingY\": 10,\n        \"textAlign\": \"start\",\n        \"fontWeight\": \"bold\",\n        \"borderWidth\": 0,\n        \"borderColor\": \"#ffffff\",\n        \"borderRadius\": 5,\n        \"letterSpacing\": 5,\n        \"writingMode\": \"horizontal-tb\",\n        \"backgroundColor\": \"#00000000\"\n      }\n    }\n  ],\n  \"requestGlobalConfig\": {\n    \"requestDataPond\": [\n      {\n        \"dataPondId\": \"5mb1sm98wag000\",\n        \"dataPondName\": \"5mb1sm98wag000\",\n        \"dataPondRequestConfig\": {\n          \"requestDataType\": 2,\n          \"requestHttpType\": \"patch\",\n          \"requestUrl\": \"/sys/testData\",\n          \"requestInterval\": 5,\n          \"requestIntervalUnit\": \"second\",\n          \"requestContentType\": 0,\n          \"requestParamsBodyType\": \"none\",\n          \"requestSQLContent\": {\n            \"sql\": \"select * from  where\"\n          },\n          \"requestParams\": {\n            \"Body\": {\n              \"form-data\": {},\n              \"x-www-form-urlencoded\": {},\n              \"json\": \"\",\n              \"xml\": \"\"\n            },\n            \"Header\": {},\n            \"Params\": {}\n          }\n        }\n      }\n    ],\n    \"requestOriginUrl\": \"http://127.0.0.1:8090/api/goview\",\n    \"requestInterval\": 30,\n    \"requestIntervalUnit\": \"second\",\n    \"requestParams\": {\n      \"Body\": {\n        \"form-data\": {},\n        \"x-www-form-urlencoded\": {},\n        \"json\": \"\",\n        \"xml\": \"\"\n      },\n      \"Header\": {},\n      \"Params\": {}\n    }\n  }\n}', 1, '32211', 1, '2024-03-04 11:44:40', '2024-03-04 09:30:24', NULL);

--
-- 转储表的索引
--

--
-- 表的索引 `hg_admin_attachment`
--
ALTER TABLE `hg_admin_attachment`
  ADD PRIMARY KEY (`id`),
  ADD KEY `md5` (`md5`);

--
-- 表的索引 `hg_admin_member`
--
ALTER TABLE `hg_admin_member`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `hg_admin_project`
--
ALTER TABLE `hg_admin_project`
  ADD PRIMARY KEY (`id`) USING BTREE,
  ADD KEY `created_by` (`created_by`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `hg_admin_attachment`
--
ALTER TABLE `hg_admin_attachment`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID', AUTO_INCREMENT=24;

--
-- 使用表AUTO_INCREMENT `hg_admin_member`
--
ALTER TABLE `hg_admin_member`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID', AUTO_INCREMENT=13;

--
-- 使用表AUTO_INCREMENT `hg_admin_project`
--
ALTER TABLE `hg_admin_project`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '项目id', AUTO_INCREMENT=20240304092998015;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
