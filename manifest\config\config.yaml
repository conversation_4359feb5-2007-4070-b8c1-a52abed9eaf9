# hotgo配置
hotgo:
  # debug开关，开启后接口出现错误时会向前端输出堆栈信息，可选：false|true，默认为true
  debug: true

# 路由配置
router:
  # 后台
  admin:
    # 前缀
    prefix: "/admin"
    # 不需要验证登录的路由地址
    exceptLogin: [
        "/api/goview/project/getData", # 获取项目数据
      ]

# 统一默认日志配置
defaultLogger: &defaultLogger
  level: "all"
  flags: 42
  file: "{Y-m-d}.log" # 日志文件格式。默认为"{Y-m-d}.log"
  stdoutColorDisabled: false # 关闭终端的颜色打印。可选：false|true，默认false
  writerColorEnable: false # 日志文件是否带上颜色。可选：false|true，默认false，表示不带颜色
  rotateExpire: "7d" # 日志保留天数
  rotateBackupLimit: 2 # 最大备份数量
  rotateBackupCompress: 2 # 日志文件压缩级别，0-9,9最高

# gf配置，配置参考：https://goframe.org/pages/viewpage.action?pageId=44449486
server:
  address: ":8090" # 本地监听地址
  openapiPath: "/api.json" # OpenAPI接口文档地址
  swaggerPath: "/swagger" # 内置SwaggerUI展示地址
  serverRoot: "resource/public" # 静态文件服务的目录根路径，配置时自动开启静态文件服务。
  DumpRouterMap: false # 是否在Server启动时打印所有的路由列表。
  logPath: "logs/server" # 服务日志保存路径
  ErrorStack: true # 当Server捕获到异常时是否记录堆栈信息到日志中。默认为true
  ErrorLogEnabled: true # 是否记录异常日志信息到日志中。默认为true
  errorLogPattern: "error/{Y-m-d}.log" # 异常错误日志文件格式。默认为"error-{Ymd}.log"
  accessLogEnabled: true # 是否记录访问日志。默认为false
  accessLogPattern: "access/{Y-m-d}.log" # 访问日志文件格式。默认为"access-{Ymd}.log"
  maxHeaderBytes: "100KB" # 请求头大小限制，请求头包括客户端提交的Cookie数据，默认设置为100KB
  clientMaxBodySize: "300MB" # 客户端提交的Body大小限制，同时也影响文件上传大小，默认设置为300MB
  serverAgent: "HG HTTP Server" # 服务端Agent信息。默认为"HG HTTP Server"
  # PProf配置
  pprofEnabled: true # 是否开启PProf性能调试特性。默认为false
  pprofPattern: "/pprof" # 开启PProf时有效，表示PProf特性的页面访问路径，对当前Server绑定的所有域名有效。
  # 服务日志配置
  logger:
    path: "logs/server" # 日志文件路径。默认为空，表示关闭，仅输出到终端
    <<: *defaultLogger

#缓存
cache:
  adapter: "file" # 缓存驱动方式，支持：memory|redis|file，不填默认memory
  fileDir: "./storage/cache" # 文件缓存路径，adapter=file时必填

# 登录令牌
token:
  secretKey: "hotgo123" # 令牌加密秘钥，考虑安全问题生产环境中请修改默认值
  expires: 604800 # 令牌有效期，单位：秒。默认7天
  autoRefresh: true # 是否开启自动刷新过期时间， false|true 默认为true
  refreshInterval: 86400 # 刷新间隔，单位：秒。必须小于expires，否则无法触发。默认1天内只允许刷新一次
  maxRefreshTimes: 30 # 最大允许刷新次数，-1不限制。默认30次
  multiLogin: true # 是否允许多端登录， false|true 默认为true

# 上传驱动
storager:
  # 通用配置
  drive: "local" # 上传驱动。local：本地存储
  fileSize: "10" # 上传图片大小限制，单位：MB
  fileType: "doc,docx,pdf,zip,tar,xls,xlsx,rar,jpg,jpeg,gif,npm,png,svg" # 上传文件类型限制，文件上传后缀类型限制
  imageSize: "your_image_size_value" # 上传图片大小限制，单位：MB
  imageType: "jpg,jpeg,gif,npm,png,svg" # 上传图片类型限制，图片上传后缀类型限制
  localPath: "attachment/" # 本地存储路径，对外访问的相对路径

# Redis. 配置参考：https://goframe.org/pages/viewpage.action?pageId=1114217
redis:
  default:
    address: "127.0.0.1:6379"
    db: "0"
    pass: ""
    idleTimeout: "20"

# Database. 配置参考：https://goframe.org/pages/viewpage.action?pageId=1114245
database:
  # 数据库类型配置，支持：mysql, sqlite
  type: "sqlite" # 数据库类型，可选：mysql, sqlite
  logger:
    path: "logs/database" # 日志文件路径。默认为空，表示关闭，仅输出到终端
    <<: *defaultLogger
    stdout: true
  # 当前使用的数据库配置 (根据 type 字段切换)
  default:
    link: "sqlite::@file(storage/data/goview.db)"
    debug: true
    Prefix: "hg_"
  # MySQL 配置 (当 type = "mysql" 时，将此配置复制到 default 节点)
  mysql:
    link: "mysql:goview:nWkG43Xxnbi2Y44C@tcp(127.0.0.1:3306)/goview?loc=Local&parseTime=true&charset=utf8mb4"
    debug: true
    Prefix: "hg_"
  # SQLite 配置 (当 type = "sqlite" 时，需要手动将此配置复制到 default 节点)
  sqlite:
    link: "sqlite::@file(storage/data/goview.db)"
    debug: true
    Prefix: "hg_"
