// Package admin
// @Description
// <AUTHOR> <<EMAIL>>
package admin

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
	"hotgo/internal/dao"
	"hotgo/internal/library/contexts"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/adminin"
	"hotgo/internal/service"
)

type sAdminProject struct{}

func NewAdminProject() *sAdminProject {
	return &sAdminProject{}
}

func init() {
	service.RegisterAdminProject(NewAdminProject())
}

// Model ORM模型
func (s *sAdminProject) Model(ctx context.Context) *gdb.Model {
	return dao.AdminProject.Ctx(ctx).Handler(func(m *gdb.Model) *gdb.Model {
		return m.Where(dao.AdminProject.Columns().CreatedBy, contexts.GetUserId(ctx))
	})
}

// List 查询列表
func (s *sAdminProject) List(ctx context.Context, in *adminin.ProjectListInp) (list []*adminin.ProjectListModel, totalCount int, err error) {
	mod := s.Model(ctx)
	totalCount, err = mod.Count()
	if err != nil {
		return
	}

	if totalCount == 0 {
		return
	}

	if err = mod.Page(in.Page, in.PerPage).OrderDesc(dao.AdminProject.Columns().Id).Scan(&list); err != nil {
		return
	}

	for _, v := range list {
		v.State = v.Status
		v.CreateUserId = v.CreatedBy
		v.CreateTime = v.CreatedAt
	}
	return
}

// Delete 删除
func (s *sAdminProject) Delete(ctx context.Context, in *adminin.ProjectDeleteInp) (err error) {
	if exists, err := s.ExistById(ctx, in.Id); err != nil || !exists {
		err = gerror.New("没有找到项目！")
		return err
	}
	_, err = s.Model(ctx).WherePri(in.Id).Delete()
	return
}

// Create 新增
func (s *sAdminProject) Create(ctx context.Context, in *adminin.ProjectCreateInp) (res *adminin.ProjectCreateModel, err error) {
	data := &entity.AdminProject{
		Id:          gconv.Int64(fmt.Sprintf("%v%v", gtime.Now().Format("YmdH"), grand.N(10000, 99999))),
		ProjectName: in.ProjectName,
		IndexImage:  in.IndexImage,
		Status:      -1,
		Remarks:     in.Remarks,
		CreatedBy:   contexts.GetUserId(ctx),
	}

	if _, err = dao.AdminProject.Ctx(ctx).Data(data).Insert(); err != nil {
		return nil, err
	}

	res = &adminin.ProjectCreateModel{
		Id:           data.Id,
		ProjectName:  data.ProjectName,
		State:        data.Status,
		CreateTime:   gtime.Now(),
		CreateUserId: contexts.GetUserId(ctx),
		IndexImage:   data.IndexImage,
		Remarks:      data.Remarks,
	}
	return
}

// Edit 修改
func (s *sAdminProject) Edit(ctx context.Context, in *adminin.ProjectEditInp) (err error) {
	if exists, err := s.ExistById(ctx, in.Id); err != nil || !exists {
		err = gerror.New("没有找到项目！")
		return err
	}

	data := make(g.Map)
	if len(in.IndexImage) > 0 {
		data["index_image"] = in.IndexImage
	}
	if len(in.ProjectName) > 0 {
		data["project_name"] = in.ProjectName
	}
	if len(in.Remarks) > 0 {
		data["remarks"] = in.Remarks
	}

	if len(data) > 0 {
		_, err = s.Model(ctx).WherePri(in.Id).Data(data).Update()
	}
	return
}

// SaveData 保存数据
func (s *sAdminProject) SaveData(ctx context.Context, in *adminin.ProjectSaveDataInp) (err error) {
	if exists, err := s.ExistById(ctx, in.Id); err != nil || !exists {
		err = gerror.New("没有找到项目！")
		return err
	}
	_, err = s.Model(ctx).WherePri(in.Id).Data("content", in.Content).Update()
	return
}

// Publish 修改发布状态
func (s *sAdminProject) Publish(ctx context.Context, in *adminin.ProjectPublishInp) (err error) {
	if exists, err := s.ExistById(ctx, in.Id); err != nil || !exists {
		err = gerror.New("没有找到项目！")
		return err
	}
	_, err = s.Model(ctx).WherePri(in.Id).Data("status", in.State).Update()
	return
}

// GetData 获取指定信息
func (s *sAdminProject) GetData(ctx context.Context, in *adminin.ProjectGetDataInp) (res *adminin.ProjectGetDataModel, err error) {
	if err = dao.AdminProject.Ctx(ctx).WherePri(in.Id).Scan(&res); err != nil {
		return nil, err
	}

	if res == nil {
		err = gerror.New("项目不存在")
		return
	}

	res.State = res.Status
	return
}

// ExistById 判断指定项目是否存在
func (s *sAdminProject) ExistById(ctx context.Context, id int64) (exists bool, err error) {
	count, err := s.Model(ctx).WherePri(id).Count()
	if err != nil {
		return
	}
	exists = count > 0
	return
}
