// Package main
// 数据库切换工具
// 使用方法: go run scripts/switch_database.go [mysql|sqlite]
package main

import (
	"fmt"
	"os"
	"strings"

	"github.com/gogf/gf/v2/os/gfile"
)

const configFile = "manifest/config/config.yaml"

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run scripts/switch_database.go [mysql|sqlite]")
		fmt.Println("示例:")
		fmt.Println("  go run scripts/switch_database.go mysql   # 切换到 MySQL")
		fmt.Println("  go run scripts/switch_database.go sqlite  # 切换到 SQLite")
		os.Exit(1)
	}

	dbType := strings.ToLower(os.Args[1])
	if dbType != "mysql" && dbType != "sqlite" {
		fmt.Printf("错误: 不支持的数据库类型 '%s'，只支持 mysql 或 sqlite\n", dbType)
		os.Exit(1)
	}

	err := switchDatabase(dbType)
	if err != nil {
		fmt.Printf("切换数据库失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("成功切换到 %s 数据库\n", dbType)
}

func switchDatabase(dbType string) error {
	if !gfile.Exists(configFile) {
		return fmt.Errorf("配置文件不存在: %s", configFile)
	}

	content := gfile.GetContents(configFile)
	if content == "" {
		return fmt.Errorf("配置文件内容为空")
	}

	lines := strings.Split(content, "\n")
	var newLines []string
	inDatabaseSection := false
	
	for i, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		
		// 检测是否进入 database 配置段
		if strings.HasPrefix(trimmedLine, "database:") {
			inDatabaseSection = true
			newLines = append(newLines, line)
			continue
		}
		
		// 检测是否离开 database 配置段
		if inDatabaseSection && strings.HasPrefix(line, "#") == false && 
		   strings.HasPrefix(line, " ") == false && 
		   strings.HasPrefix(line, "\t") == false && 
		   trimmedLine != "" {
			inDatabaseSection = false
		}
		
		// 在 database 配置段内处理
		if inDatabaseSection {
			// 修改 type 配置
			if strings.Contains(trimmedLine, "type:") {
				indent := getIndent(line)
				newLines = append(newLines, fmt.Sprintf("%stype: \"%s\" # 数据库类型，可选：mysql, sqlite", indent, dbType))
				continue
			}
			
			// 处理 default 配置段
			if strings.Contains(trimmedLine, "default:") {
				newLines = append(newLines, line)
				
				// 根据数据库类型添加相应的配置
				if dbType == "mysql" {
					newLines = append(newLines, addMySQLConfig(lines, i+1)...)
				} else {
					newLines = append(newLines, addSQLiteConfig(lines, i+1)...)
				}
				
				// 跳过原有的 default 配置内容
				i = skipDefaultConfig(lines, i+1) - 1
				continue
			}
		}
		
		newLines = append(newLines, line)
	}

	newContent := strings.Join(newLines, "\n")
	return gfile.PutContents(configFile, newContent)
}

func getIndent(line string) string {
	for i, char := range line {
		if char != ' ' && char != '\t' {
			return line[:i]
		}
	}
	return ""
}

func addMySQLConfig(lines []string, startIndex int) []string {
	indent := "    "
	return []string{
		indent + "link: \"mysql:goview:nWkG43Xxnbi2Y44C@tcp(127.0.0.1:3306)/goview?loc=Local&parseTime=true&charset=utf8mb4\"",
		indent + "debug: true",
		indent + "Prefix: \"hg_\"",
	}
}

func addSQLiteConfig(lines []string, startIndex int) []string {
	indent := "    "
	return []string{
		indent + "link: \"sqlite::@file(storage/data/goview.db)\"",
		indent + "debug: true",
		indent + "Prefix: \"hg_\"",
	}
}

func skipDefaultConfig(lines []string, startIndex int) int {
	for i := startIndex; i < len(lines); i++ {
		line := lines[i]
		trimmedLine := strings.TrimSpace(line)
		
		// 如果遇到下一个配置段或者非缩进行，停止跳过
		if trimmedLine != "" && !strings.HasPrefix(line, "    ") && !strings.HasPrefix(line, "\t") {
			return i
		}
	}
	return len(lines)
}
