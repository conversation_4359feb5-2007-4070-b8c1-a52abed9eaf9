-- SQLite 数据库初始化脚本
-- 从 MySQL 版本转换而来
-- 创建时间: 2024-03-04

-- 表的结构 `hg_admin_attachment`
CREATE TABLE IF NOT EXISTS `hg_admin_attachment` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `app_id` TEXT NOT NULL DEFAULT '',
  `member_id` INTEGER DEFAULT 0,
  `cate_id` INTEGER DEFAULT 0,
  `drive` TEXT DEFAULT NULL,
  `name` TEXT DEFAULT NULL,
  `kind` TEXT DEFAULT NULL,
  `mime_type` TEXT NOT NULL DEFAULT '',
  `naive_type` TEXT NOT NULL DEFAULT '',
  `path` TEXT DEFAULT NULL,
  `file_url` TEXT DEFAULT NULL,
  `size` INTEGER DEFAULT 0,
  `ext` TEXT DEFAULT NULL,
  `md5` TEXT DEFAULT NULL,
  `status` INTEGER NOT NULL DEFAULT 1,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 表的结构 `hg_admin_member`
CREATE TABLE IF NOT EXISTS `hg_admin_member` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `nickname` TEXT DEFAULT NULL,
  `username` TEXT NOT NULL DEFAULT '',
  `password_hash` TEXT NOT NULL DEFAULT '',
  `salt` TEXT NOT NULL DEFAULT '',
  `avatar` TEXT DEFAULT NULL,
  `last_active_at` DATETIME DEFAULT NULL,
  `remark` TEXT DEFAULT NULL,
  `status` INTEGER DEFAULT 1,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认管理员数据
INSERT OR IGNORE INTO `hg_admin_member` (`id`, `nickname`, `username`, `password_hash`, `salt`, `avatar`, `last_active_at`, `remark`, `status`, `created_at`, `updated_at`) VALUES
(1, '孟帅', 'admin', 'ce72f929f1acdd4f21a5f8337e6a9aed', '6541561', 'http://bufanyun.cn-bj.ufileos.com/hotgo/attachment/2023-02-09/cqdq8er9nfkchdopav.png', '2024-03-01 17:19:34', NULL, 1, '2024-03-01 17:09:45', '2024-03-01 17:19:34');

-- 表的结构 `hg_admin_project`
CREATE TABLE IF NOT EXISTS `hg_admin_project` (
  `id` INTEGER PRIMARY KEY,
  `project_name` TEXT DEFAULT NULL,
  `index_image` TEXT DEFAULT NULL,
  `content` TEXT DEFAULT NULL,
  `status` INTEGER NOT NULL DEFAULT -1,
  `remarks` TEXT DEFAULT NULL,
  `created_by` INTEGER NOT NULL DEFAULT 0,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted_at` DATETIME DEFAULT NULL
);

-- 插入示例项目数据
INSERT OR IGNORE INTO `hg_admin_project` (`id`, `project_name`, `index_image`, `content`, `status`, `remarks`, `created_by`, `updated_at`, `created_at`, `deleted_at`) VALUES
(202403041750, '测试项目', '', '{"config":{"width":1920,"height":1080,"chartThemeColor":"dark","chartThemeSetting":{"backgroundColor":"rgba(0,0,0,0)","titleColor":"#B9B8CE","showTitle":true,"titleFontSize":18,"titleFontWeight":"bold","showUnit":true,"unitColor":"#B9B8CE","unitFontSize":12,"unitFontWeight":"normal","showTooltip":true,"tooltipColor":"#1A1A1A","tooltipBackgroundColor":"rgba(0, 0, 0, 0.65)","tooltipBorderColor":"#fefefe","tooltipBorderWidth":0,"tooltipBorderRadius":6,"tooltipShadowBlur":25,"tooltipShadowColor":"rgba(0, 0, 0, 0.25)","tooltipShadowOffsetX":0,"tooltipShadowOffsetY":0,"tooltipExtraCSS":"","tooltipFormatter":"","globalColorSwatches":["#74a9d8","#7dc855","#f4c430","#ff6b6b","#a78bfa","#06d6a0","#f72585","#4cc9f0","#7209b7","#2d6a4f"],"globalColorSwatchesSecondary":["#74a9d8","#7dc855","#f4c430","#ff6b6b","#a78bfa","#06d6a0","#f72585","#4cc9f0","#7209b7","#2d6a4f"]},"attr":{"nodeThemeColor":"dark","chartInitConfig":{"renderer":"canvas","devicePixelRatio":1,"locale":"ZH","animation":true,"animationThreshold":2000,"animationDuration":1000,"animationDurationUpdate":300,"animationEasing":"cubicOut","animationEasingUpdate":"cubicOut","animationDelay":0,"animationDelayUpdate":0},"requestConfig":{"requestDataType":"static","requestHttpType":"get","requestUrl":"","requestInterval":null,"requestIntervalUnit":"second","requestContentType":"application/json"},"filterShow":false,"chartFrame":"common"},"option":{"dataset":{"dimensions":["product","2015","2016","2017"],"source":[{"product":"Matcha Latte","2015":43.3,"2016":85.8,"2017":93.7},{"product":"Milk Tea","2015":83.1,"2016":73.4,"2017":55.1},{"product":"Cheese Cocoa","2015":86.4,"2016":65.2,"2017":82.5},{"product":"Walnut Brownie","2015":72.4,"2016":53.9,"2017":39.1}]},"xAxis":{"type":"category","show":true,"name":"","nameGap":15,"nameTextStyle":{"color":"#B9B8CE","fontSize":12},"inverse":false,"boundaryGap":true,"splitLine":{"show":false,"lineStyle":{"color":"#484753","width":1,"type":"solid"}},"axisLabel":{"show":true,"fontSize":12,"color":"#B9B8CE","rotate":0,"formatter":"","rich":{}},"axisLine":{"show":true,"lineStyle":{"color":"#B9B8CE","width":1,"type":"solid"}},"axisTick":{"show":true,"length":5,"lineStyle":{"color":"#B9B8CE","width":1,"type":"solid"}}},"yAxis":{"type":"value","show":true,"name":"","nameGap":15,"nameTextStyle":{"color":"#B9B8CE","fontSize":12},"inverse":false,"boundaryGap":false,"splitLine":{"show":true,"lineStyle":{"color":"#484753","width":1,"type":"solid"}},"axisLabel":{"show":true,"fontSize":12,"color":"#B9B8CE","rotate":0,"formatter":"","rich":{}},"axisLine":{"show":false,"lineStyle":{"color":"#B9B8CE","width":1,"type":"solid"}},"axisTick":{"show":false,"length":5,"lineStyle":{"color":"#B9B8CE","width":1,"type":"solid"}}},"series":[{"type":"bar","barWidth":null,"barMaxWidth":"20%","itemStyle":{"color":null,"borderRadius":0},"label":{"show":false,"position":"top","color":"#fff","fontSize":12},"labelLine":{"show":true},"encode":{"x":"product","y":"2015"}},{"type":"bar","barWidth":null,"barMaxWidth":"20%","itemStyle":{"color":null,"borderRadius":0},"label":{"show":false,"position":"top","color":"#fff","fontSize":12},"labelLine":{"show":true},"encode":{"x":"product","y":"2016"}},{"type":"bar","barWidth":null,"barMaxWidth":"20%","itemStyle":{"color":null,"borderRadius":0},"label":{"show":false,"position":"top","color":"#fff","fontSize":12},"labelLine":{"show":true},"encode":{"x":"product","y":"2017"}}],"backgroundColor":"rgba(0,0,0,0)","title":{"text":"柱状图","left":"center","textStyle":{"color":"#B9B8CE","fontSize":18,"fontWeight":"bold"}},"tooltip":{"show":true,"trigger":"axis","axisPointer":{"type":"shadow"},"backgroundColor":"rgba(0, 0, 0, 0.65)","borderColor":"#fefefe","borderWidth":0,"borderRadius":6,"textStyle":{"color":"#1A1A1A","fontSize":14},"extraCssText":"box-shadow: 0 0 25px rgba(0, 0, 0, 0.25);"},"legend":{"show":true,"top":"5%","textStyle":{"color":"#B9B8CE"}},"grid":{"left":"10%","right":"10%","top":"20%","bottom":"15%"},"toolbox":{"show":false,"feature":{"saveAsImage":{}}},"brush":{"toolbox":["rect","polygon","lineX","lineY","keep","clear"],"xAxisIndex":"all"},"dataZoom":[{"type":"inside","xAxisIndex":[0],"yAxisIndex":[0]},{"type":"slider","xAxisIndex":[0],"yAxisIndex":[0],"show":false,"bottom":"0%"}],"animation":true}}', 1, '', 1, '2024-03-04 17:50:00', '2024-03-04 17:50:00', NULL);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_admin_member_username ON hg_admin_member(username);
CREATE INDEX IF NOT EXISTS idx_admin_project_created_by ON hg_admin_project(created_by);
CREATE INDEX IF NOT EXISTS idx_admin_project_status ON hg_admin_project(status);
CREATE INDEX IF NOT EXISTS idx_admin_attachment_member_id ON hg_admin_attachment(member_id);
