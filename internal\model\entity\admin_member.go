// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminMember is the golang structure for table admin_member.
type AdminMember struct {
	Id           int64       `json:"id"           description:"管理员ID"`
	Nickname     string      `json:"nickname"     description:"昵称"`
	Username     string      `json:"username"     description:"帐号"`
	PasswordHash string      `json:"passwordHash" description:"密码"`
	Salt         string      `json:"salt"         description:"密码盐"`
	Avatar       string      `json:"avatar"       description:"头像"`
	LastActiveAt *gtime.Time `json:"lastActiveAt" description:"最后活跃时间"`
	Remark       string      `json:"remark"       description:"备注"`
	Status       int         `json:"status"       description:"状态"`
	CreatedAt    *gtime.Time `json:"createdAt"    description:"创建时间"`
	UpdatedAt    *gtime.Time `json:"updatedAt"    description:"修改时间"`
}
