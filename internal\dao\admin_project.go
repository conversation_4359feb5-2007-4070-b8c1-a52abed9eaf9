// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalAdminProjectDao is internal type for wrapping internal DAO implements.
type internalAdminProjectDao = *internal.AdminProjectDao

// adminProjectDao is the data access object for table hg_admin_project.
// You can define custom methods on it to extend its functionality as you wish.
type adminProjectDao struct {
	internalAdminProjectDao
}

var (
	// AdminProject is globally public accessible object for table hg_admin_project operations.
	AdminProject = adminProjectDao{
		internal.NewAdminProjectDao(),
	}
)

// Fill with you ideas below.
