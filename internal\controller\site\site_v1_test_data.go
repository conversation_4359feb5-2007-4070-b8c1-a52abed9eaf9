package site

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"hotgo/internal/library/response"
	"hotgo/utility/format"
	"math/rand"

	"hotgo/api/site/v1"
)

func (c *ControllerV1) TestData(ctx context.Context, req *v1.TestDataReq) (res *v1.TestDataRes, err error) {
	res = new(v1.TestDataRes)

	// 模拟设备信息
	res.DeviceInfo = map[string]interface{}{
		"deviceId":     "IoTDevice001",
		"GPSLongitude": "73.56789",
		"GPSLatitude":  "-42.12345",
		"location":     "河南郑州高新区科学大道001号",
		"deviceStatus": true,
		"deviceTime":   gtime.Now(),
	}

	fields := []string{
		"temperature", "humidity", "pressure", "flow",
		"factoryData", "voltage", "current", "waterLevel",
		"airQuality", "vibration", "noiseLevel", "ambientLight",
		"windSpeed", "deviceTemperature", "deviceVibration",
		"deviceVoltage", "deviceCurrent", "powerConsumption",
	}
	res.DeviceData = make(map[string]interface{}, len(fields))
	for _, field := range fields {
		// 模拟设备上报随机数值
		res.DeviceData[field] = format.Round2Float64(rand.Float64() * 100)
	}

	// 自定义返回字段格式
	response.CustomJson(ghttp.RequestFromCtx(ctx), g.Map{
		"code": 200,
		"data": res,
		"msg":  "获取设备数据成功",
	})
	return
}
