// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"hotgo/internal/model"
	"hotgo/internal/model/input/adminin"
)

type (
	IAdminProject interface {
		// List 查询列表
		List(ctx context.Context, in *adminin.ProjectListInp) (list []*adminin.ProjectListModel, totalCount int, err error)
		// Delete 删除
		Delete(ctx context.Context, in *adminin.ProjectDeleteInp) (err error)
		// Create 新增
		Create(ctx context.Context, in *adminin.ProjectCreateInp) (res *adminin.ProjectCreateModel, err error)
		// Edit 修改
		Edit(ctx context.Context, in *adminin.ProjectEditInp) (err error)
		// SaveData 保存数据
		SaveData(ctx context.Context, in *adminin.ProjectSaveDataInp) (err error)
		// Publish 修改发布状态
		Publish(ctx context.Context, in *adminin.ProjectPublishInp) (err error)
		// GetData 获取指定信息
		GetData(ctx context.Context, in *adminin.ProjectGetDataInp) (res *adminin.ProjectGetDataModel, err error)
	}
	IAdminSite interface {
		// Register 账号注册
		Register(ctx context.Context, in *adminin.RegisterInp) (err error)
		// AccountLogin 账号登录
		AccountLogin(ctx context.Context, in *adminin.AccountLoginInp) (res *adminin.LoginModel, err error)
		// BindUserContext 绑定用户上下文
		BindUserContext(ctx context.Context, claims *model.Identity) (err error)
	}
)

var (
	localAdminProject IAdminProject
	localAdminSite    IAdminSite
)

func AdminProject() IAdminProject {
	if localAdminProject == nil {
		panic("implement not found for interface IAdminProject, forgot register?")
	}
	return localAdminProject
}

func RegisterAdminProject(i IAdminProject) {
	localAdminProject = i
}

func AdminSite() IAdminSite {
	if localAdminSite == nil {
		panic("implement not found for interface IAdminSite, forgot register?")
	}
	return localAdminSite
}

func RegisterAdminSite(i IAdminSite) {
	localAdminSite = i
}
