# Database switch script
# Usage: .\scripts\switch_database.ps1 [mysql|sqlite]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("mysql", "sqlite")]
    [string]$DatabaseType
)

$configFile = "manifest\config\config.yaml"

if (-not (Test-Path $configFile)) {
    Write-Error "Config file not found: $configFile"
    exit 1
}

Write-Host "Switching to $DatabaseType database..." -ForegroundColor Green

try {
    $content = Get-Content $configFile -Raw

    # Update database type
    $content = $content -replace 'type:\s*"[^"]*"', "type: `"$DatabaseType`""

    if ($DatabaseType -eq "sqlite") {
        # Switch to SQLite config
        $content = $content -replace 'link:\s*"mysql:[^"]*"', 'link: "sqlite::@file(storage/data/goview.db)"'
        Write-Host "Switched to SQLite database config" -ForegroundColor Yellow
        Write-Host "Database file location: storage/data/goview.db" -ForegroundColor Cyan
    } else {
        # Switch to MySQL config
        $content = $content -replace 'link:\s*"sqlite:[^"]*"', 'link: "mysql:goview:nWkG43Xxnbi2Y44C@tcp(127.0.0.1:3306)/goview?loc=Local&parseTime=true&charset=utf8mb4"'
        Write-Host "Switched to MySQL database config" -ForegroundColor Yellow
        Write-Host "Please ensure MySQL service is running and database 'goview' exists" -ForegroundColor Cyan
    }

    # Save modified config
    Set-Content $configFile -Value $content -Encoding UTF8

    Write-Host "Database configuration switched successfully!" -ForegroundColor Green
    Write-Host "Please restart the application for changes to take effect." -ForegroundColor Magenta

} catch {
    Write-Error "Database switch error: $($_.Exception.Message)"
    exit 1
}
