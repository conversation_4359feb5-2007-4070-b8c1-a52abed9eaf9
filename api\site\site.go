// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package site

import (
	"context"

	"hotgo/api/site/v1"
)

type ISiteV1 interface {
	Register(ctx context.Context, req *v1.RegisterReq) (res *v1.RegisterRes, err error)
	AccountLogin(ctx context.Context, req *v1.AccountLoginReq) (res *v1.AccountLoginRes, err error)
	LoginLogout(ctx context.Context, req *v1.LoginLogoutReq) (res *v1.LoginLogoutRes, err error)
	GetOssInfo(ctx context.Context, req *v1.GetOssInfoReq) (res *v1.GetOssInfoRes, err error)
	TestData(ctx context.Context, req *v1.TestDataReq) (res *v1.TestDataRes, err error)
}
