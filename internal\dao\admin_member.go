// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalAdminMemberDao is internal type for wrapping internal DAO implements.
type internalAdminMemberDao = *internal.AdminMemberDao

// adminMemberDao is the data access object for table hg_admin_member.
// You can define custom methods on it to extend its functionality as you wish.
type adminMemberDao struct {
	internalAdminMemberDao
}

var (
	// AdminMember is globally public accessible object for table hg_admin_member operations.
	AdminMember = adminMemberDao{
		internal.NewAdminMemberDao(),
	}
)

// Fill with you ideas below.
