// Package storager
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package storager

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/internal/model"
)

var config *model.UploadConfig

func InitConfig(ctx context.Context) {
	err := g.Cfg().MustGet(ctx, "storager").Scan(&config)
	if err != nil {
		panic(err)
	}

	if config == nil {
		panic("没有找到storager配置")
	}
}

func GetModel(ctx context.Context) *gdb.Model {
	return g.Model("admin_attachment").Ctx(ctx)
}
