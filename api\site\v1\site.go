// Package v1
// @Description
// <AUTHOR> <<EMAIL>>
package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/internal/model/input/adminin"
)

// RegisterReq 提交账号注册
type RegisterReq struct {
	g.Meta `path:"/sys/register" method:"post" tags:"后台基础" summary:"账号注册"`
	adminin.RegisterInp
}

type RegisterRes struct {
	*adminin.LoginModel
}

// AccountLoginReq 提交账号登录
type AccountLoginReq struct {
	g.Meta `path:"/sys/login" method:"post" tags:"后台基础" summary:"账号登录"`
	adminin.AccountLoginInp
}

type AccountLoginRes struct {
	*adminin.LoginModel
}

// LoginLogoutReq 注销登录
type LoginLogoutReq struct {
	g.Meta `path:"/sys/logout" method:"get" tags:"后台基础" summary:"注销登录"`
}

type LoginLogoutRes struct{}

// GetOssInfoReq 获取文件上传oss信息
type GetOssInfoReq struct {
	g.Meta `path:"/sys/getOssInfo" method:"get" tags:"后台基础" summary:"获取文件上传oss信息"`
}

type GetOssInfoRes struct {
	BucketURL string `json:"bucketUrl"`
}

// TestDataReq 模拟测试数据
type TestDataReq struct {
	g.Meta `path:"/sys/testData" method:"all" tags:"后台基础" summary:"模拟测试数据"`
}

type TestDataRes struct {
	g.Meta     `mime:"custom"`
	DeviceInfo map[string]interface{} `json:"deviceInfo" dc:"设备信息"`
	DeviceData map[string]interface{} `json:"deviceData" dc:"随机设备数值"`
}
