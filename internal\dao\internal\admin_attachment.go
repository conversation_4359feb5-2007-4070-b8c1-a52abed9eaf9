// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdminAttachmentDao is the data access object for table hg_admin_attachment.
type AdminAttachmentDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of current DAO.
	columns AdminAttachmentColumns // columns contains all the column names of Table for convenient usage.
}

// AdminAttachmentColumns defines and stores column names for table hg_admin_attachment.
type AdminAttachmentColumns struct {
	Id        string // 文件ID
	AppId     string // 应用ID
	MemberId  string // 管理员ID
	CateId    string // 上传分类
	Drive     string // 上传驱动
	Name      string // 文件原始名
	Kind      string // 上传类型
	MimeType  string // 扩展类型
	NaiveType string // NaiveUI类型
	Path      string // 本地路径
	FileUrl   string // url
	Size      string // 文件大小
	Ext       string // 扩展名
	Md5       string // md5校验码
	Status    string // 状态
	CreatedAt string // 创建时间
	UpdatedAt string // 修改时间
}

// adminAttachmentColumns holds the columns for table hg_admin_attachment.
var adminAttachmentColumns = AdminAttachmentColumns{
	Id:        "id",
	AppId:     "app_id",
	MemberId:  "member_id",
	CateId:    "cate_id",
	Drive:     "drive",
	Name:      "name",
	Kind:      "kind",
	MimeType:  "mime_type",
	NaiveType: "naive_type",
	Path:      "path",
	FileUrl:   "file_url",
	Size:      "size",
	Ext:       "ext",
	Md5:       "md5",
	Status:    "status",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewAdminAttachmentDao creates and returns a new DAO object for table data access.
func NewAdminAttachmentDao() *AdminAttachmentDao {
	return &AdminAttachmentDao{
		group:   "default",
		table:   "hg_admin_attachment",
		columns: adminAttachmentColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdminAttachmentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdminAttachmentDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdminAttachmentDao) Columns() AdminAttachmentColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdminAttachmentDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdminAttachmentDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdminAttachmentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
