package project

import (
	"context"
	"hotgo/api/project/v1"
	"hotgo/internal/library/storager"
)

func (c *ControllerV1) Upload(ctx context.Context, req *v1.UploadReq) (res *v1.UploadRes, err error) {
	attachment, err := storager.DoUpload(ctx, "default", req.File)
	if err != nil {
		return
	}

	res = new(v1.UploadRes)
	res.FileUrl = storager.LastUrl(ctx, attachment.FileUrl, attachment.Drive)
	return
}
