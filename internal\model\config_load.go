// Package model
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package model

// 本地配置.

// TokenConfig 登录令牌配置
type TokenConfig struct {
	SecretKey       string `json:"secretKey"`
	Expires         int64  `json:"expires"`
	AutoRefresh     bool   `json:"autoRefresh"`
	RefreshInterval int64  `json:"refreshInterval"`
	MaxRefreshTimes int64  `json:"maxRefreshTimes"`
	MultiLogin      bool   `json:"multiLogin"`
}

// UploadConfig 上传配置
type UploadConfig struct {
	Drive     string `json:"drive"`
	FileSize  int64  `json:"fileSize"`
	FileType  string `json:"fileType"`
	ImageSize int64  `json:"imageSize"`
	ImageType string `json:"imageType"`
	LocalPath string `json:"localPath"`
}
