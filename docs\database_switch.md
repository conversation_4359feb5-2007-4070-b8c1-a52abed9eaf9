# 数据库切换指南

本项目支持 MySQL 和 SQLite 两种数据库，您可以通过配置轻松地在两者之间切换。

## 支持的数据库

- **MySQL**: 适用于生产环境，性能更好，支持并发访问
- **SQLite**: 适用于开发环境或小型应用，无需额外安装数据库服务

## 快速切换方法

### 方法一：使用 PowerShell 脚本（推荐）

```powershell
# 切换到 SQLite
.\scripts\switch_database.ps1 sqlite

# 切换到 MySQL
.\scripts\switch_database.ps1 mysql
```

### 方法二：使用 Go 脚本

```bash
# 切换到 SQLite
go run scripts/switch_database.go sqlite

# 切换到 MySQL
go run scripts/switch_database.go mysql
```

### 方法三：手动修改配置文件

编辑 `manifest/config/config.yaml` 文件：

#### 切换到 SQLite

```yaml
database:
  type: "sqlite"  # 修改这里
  logger:
    path: "logs/database"
    # ... 其他日志配置
  default:
    link: "sqlite::@file(storage/data/goview.db)"  # 修改这里
    debug: true
    Prefix: "hg_"
```

#### 切换到 MySQL

```yaml
database:
  type: "mysql"  # 修改这里
  logger:
    path: "logs/database"
    # ... 其他日志配置
  default:
    link: "mysql:goview:nWkG43Xxnbi2Y44C@tcp(127.0.0.1:3306)/goview?loc=Local&parseTime=true&charset=utf8mb4"  # 修改这里
    debug: true
    Prefix: "hg_"
```

## 数据库初始化

### SQLite 初始化

当您首次切换到 SQLite 时，系统会自动：

1. 创建 `storage/data/` 目录（如果不存在）
2. 创建 `goview.db` 数据库文件
3. 执行 `storage/data/goview_sqlite.sql` 脚本创建表结构
4. 插入默认数据（包括管理员账户）

### MySQL 初始化

使用 MySQL 时，您需要：

1. 确保 MySQL 服务正在运行
2. 创建数据库：`CREATE DATABASE goview;`
3. 导入数据：`mysql -u root -p goview < storage/data/goview.sql`

## 默认管理员账户

无论使用哪种数据库，默认管理员账户都是：

- **用户名**: admin
- **密码**: admin123（请在生产环境中修改）

## 代码生成

如果您需要重新生成 DAO 代码，请相应地修改 `hack/config.yaml` 文件：

### 为 SQLite 生成代码

```yaml
gen:
  dao:
    - link: "sqlite::@file(storage/data/goview.db)"
      removePrefix: "hg_"
      descriptionTag: true
      noModelComment: true
      jsonCase: "CamelLower"
      gJsonSupport: true
      clear: false
```

### 为 MySQL 生成代码

```yaml
gen:
  dao:
    - link: "mysql:goview:nWkG43Xxnbi2Y44C@tcp(127.0.0.1:3306)/goview?loc=Local&parseTime=true"
      removePrefix: "hg_"
      descriptionTag: true
      noModelComment: true
      jsonCase: "CamelLower"
      gJsonSupport: true
      clear: false
```

然后运行：

```bash
gf gen dao
```

## 注意事项

1. **数据迁移**: 切换数据库类型时，数据不会自动迁移。如果需要保留数据，请手动导出和导入。

2. **性能差异**: SQLite 适合开发和小型应用，MySQL 适合生产环境和高并发场景。

3. **文件权限**: 使用 SQLite 时，确保应用程序对 `storage/data/` 目录有读写权限。

4. **备份**: 定期备份数据库文件（SQLite）或数据库（MySQL）。

## 故障排除

### SQLite 相关问题

- **权限错误**: 确保 `storage/data/` 目录存在且可写
- **文件锁定**: 确保没有其他程序正在使用数据库文件

### MySQL 相关问题

- **连接失败**: 检查 MySQL 服务是否运行，用户名密码是否正确
- **数据库不存在**: 确保已创建 `goview` 数据库

## 开发建议

- **开发环境**: 推荐使用 SQLite，简单快速
- **生产环境**: 推荐使用 MySQL，性能和稳定性更好
- **测试环境**: 可以使用 SQLite 进行快速测试
