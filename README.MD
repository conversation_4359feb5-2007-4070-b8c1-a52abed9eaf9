## 项目介绍

- 本项目是由 `GoLang` 实现的 `GoView` 后端接口，基于 `GoFrame` 开发
- `GoView` 是一个高效的拖拽式低代码数据可视化开发平台，将图表或页面元素封装为基础组件，无需编写代码即可制作数据大屏，减少心智负担。当然低代码也不是 “银弹”，希望所有人员都能理智看待此技术。
- 如果在使用中遇到问题，请提交 [issues](https://gitee.com/bufanyun/go-view-server/issues/new) 或联系作者
- 作者QQ：133814250


### 主要技术栈

| 名称                | 版本    |
| ------------------- | ------ |
| goframe             | 2.6.3 |
| golang-jwt              | 5.2.0  |
| 详见 `go.mod`  | 😁     |


### 运行环境
1. 下载golang安装 版本号需>=1.19
2. 国际: https://golang.org/dl/
3. 国内: https://golang.google.cn/dl/
4. 命令行运行 go 若控制台输出各类提示命令 则安装成功 输入 `go version` 确认版本大于1.19
5. 开发工具推荐 [Goland](https://www.jetbrains.com/go/)


### 使用说明

> 需要本地具有 git node golang mysql 环境

- node版本 16.14.x
- golang版本 >= v1.19
- mysql版本 >=5.7
- IDE推荐：Goland


### 安装go-view-server

一、克隆项目

```
git clone https://gitee.com/bufanyun/go-view-server.git
```

二、初始化数据库

- 项目数据库文件 [storage/data/goview.sql](storage/data/goview.sql) 创建数据库并导入
- 将`manifest/config/config.yaml`中的数据库配置改为你自己的：
```yaml
database:
  logger:
    path: "logs/database"                       # 日志文件路径。默认为空，表示关闭，仅输出到终端
    <<: *defaultLogger
    stdout: true
  default:
    link: "mysql:goview:nWkG43Xxnbi2Y44C@tcp(127.0.0.1:3306)/goview?loc=Local&parseTime=true&charset=utf8mb4"
    debug: true
    Prefix: "hg_"
```

三、 启动服务

```shell script
      # 设置国内代理，如果已经设置好了代理可以跳过
      go env -w GOPROXY=https://goproxy.io,direct
      
      # 更新包
      go mod tidy  
      
      # 启动所有服务
      go run main.go  # 热编译启动： gf run main.go
```

## 安装go-view

一、克隆项目

- 注意：`master-fetch` 分支是带有后端接口请求的分支！！
```
git clone --branch master-fetch https://gitee.com/dromara/go-view.git
```

二、修改和`go-view-server`一致的端口号

- 接口地址修改：.env
```env
# port
VITE_DEV_PORT = '8080'

# development path
VITE_DEV_PATH = 'http://127.0.0.1:8090'

# production path
VITE_PRO_PATH = 'http://127.0.0.1:8090'
```

三、 启动服务

- 安装
- 推荐使用 pnpm 管理项目，并使用 nrm 切换到阿里镜像，整体安装步骤如下：
```shell script
  # 1. 安装 pnpm
  npm install -g pnpm
  
  # 2. 安装 nrm
  npm install -g nrm
  
  # 3. 使用 nrm 添加阿里镜像
  nrm add taobao  https://registry.npmmirror.com/
  
  # 4. nrm 查看镜像列表
  nrm ls
  
  # 5. nrm 应用对应镜像
  nrm use taobao
```

- 安装项目依赖
```shell script
  # 推荐使用 pnpm
  pnpm install
  
  # 或 yarn
  yarn install
```

- 启动
```shell script
  # 推荐使用 pnpm
  pnpm dev
  
  # 或 yarn
  yarn dev
  
  # 或 Makefile（需要自行配置系统环境，谷歌 make 命令环境搭建）
  make dev
```

- 编译
```shell script
  # 推荐使用 pnpm
  pnpm run build
  
  # 或 yarn
  yarn run build
  
  # 或 Makefile
  make dist
```

四、 登录go-view
- 如果一切正常，启动 `go-view` 后可以通过浏览器打开下方地址进入 `go-view` 登录页面
```
登录地址：http://localhost:3000/
账号：admin
密码：admin
```


## 测试项目
- 在满足 `go-view` 后台接口使用的基础上，我们内置了一个测试项目和测试设备数据接口，方便日常配置大屏和报表的数据调试需求。
- 测试接口，获取测试数据：
```http request
GET http://127.0.0.1:8090/api/goview/sys/testData
```

- 响应数据内容
- `data.deviceInfo` 为模拟的设备基本信息，数据固定
- `data.deviceData` 为模拟的设备动态上报数据，数据和数值类型每次请求都会随机生成
```json
{
    "code": 200,
    "data": {
        "deviceInfo": {
            "GPSLatitude": "-42.12345",
            "GPSLongitude": "73.56789",
            "deviceId": "IoTDevice001",
            "deviceStatus": true,
            "deviceTime": "2024-03-04 10:38:20",
            "location": "河南郑州高新区科学大道001号"
        },
        "deviceData": {
            "airQuality": 66,
            "ambientLight": 22.13,
            "current": 66.3,
            "deviceCurrent": 87.85,
            "deviceTemperature": 21,
            "deviceVibration": 62.04,
            "deviceVoltage": 70,
            "factoryData": "WQLZbKQf",
            "humidity": "QMEHBqmT",
            "noiseLevel": 14,
            "powerConsumption": 5.18,
            "pressure": "mOiEPEiZ",
            "temperature": 8,
            "vibration": "DPemPsNi",
            "voltage": "YLkSKFXm",
            "waterLevel": 30,
            "windSpeed": 62.51
        }
    },
    "msg": "获取设备数据成功"
}
```

## 相关开源地址

- [go-view](https://gitee.com/dromara/go-view)
- [goframe](https://gitee.com/johng/gf)
- [go-view-server](https://gitee.com/bufanyun/go-view-server)


## 相关文档

- [go-view](https://www.mtruning.club/)
- [goframe](https://goframe.org/)