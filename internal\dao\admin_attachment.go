// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"hotgo/internal/dao/internal"
)

// internalAdminAttachmentDao is internal type for wrapping internal DAO implements.
type internalAdminAttachmentDao = *internal.AdminAttachmentDao

// adminAttachmentDao is the data access object for table hg_admin_attachment.
// You can define custom methods on it to extend its functionality as you wish.
type adminAttachmentDao struct {
	internalAdminAttachmentDao
}

var (
	// AdminAttachment is globally public accessible object for table hg_admin_attachment operations.
	AdminAttachment = adminAttachmentDao{
		internal.NewAdminAttachmentDao(),
	}
)

// Fill with you ideas below.
