// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdminProjectDao is the data access object for table hg_admin_project.
type AdminProjectDao struct {
	table   string              // table is the underlying table name of the DAO.
	group   string              // group is the database configuration group name of current DAO.
	columns AdminProjectColumns // columns contains all the column names of Table for convenient usage.
}

// AdminProjectColumns defines and stores column names for table hg_admin_project.
type AdminProjectColumns struct {
	Id          string // 项目id
	ProjectName string // 项目名称
	IndexImage  string // 预览图片url
	Content     string // 项目数据
	Status      string // 项目状态,-1: 未发布'1: 已发布
	Remarks     string // 项目备注
	CreatedBy   string // 创建人
	UpdatedAt   string // 更新时间
	CreatedAt   string // 创建时间
	DeletedAt   string // 删除时间
}

// adminProjectColumns holds the columns for table hg_admin_project.
var adminProjectColumns = AdminProjectColumns{
	Id:          "id",
	ProjectName: "project_name",
	IndexImage:  "index_image",
	Content:     "content",
	Status:      "status",
	Remarks:     "remarks",
	CreatedBy:   "created_by",
	UpdatedAt:   "updated_at",
	CreatedAt:   "created_at",
	DeletedAt:   "deleted_at",
}

// NewAdminProjectDao creates and returns a new DAO object for table data access.
func NewAdminProjectDao() *AdminProjectDao {
	return &AdminProjectDao{
		group:   "default",
		table:   "hg_admin_project",
		columns: adminProjectColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdminProjectDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdminProjectDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdminProjectDao) Columns() AdminProjectColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdminProjectDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdminProjectDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdminProjectDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
