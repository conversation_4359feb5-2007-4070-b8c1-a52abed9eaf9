package cmd

import (
	"context"
	"hotgo/internal/controller/project"
	"hotgo/internal/controller/site"
	"hotgo/internal/library/cache"
	"hotgo/internal/library/database"
	"hotgo/internal/library/storager"
	"hotgo/internal/library/token"
	"hotgo/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {

			// 初始化数据库
			if err := database.InitDatabase(ctx); err != nil {
				g.Log().Fatalf(ctx, "数据库初始化失败: %v", err)
				return err
			}

			// 设置缓存适配器
			cache.SetAdapter(ctx)

			token.InitConfig(ctx)

			storager.InitConfig(ctx)

			s := g.Server()

			// 注册全局中间件
			s.BindMiddleware("/*any", []ghttp.HandlerFunc{
				service.Middleware().Ctx,             // 初始化请求上下文，一般需要第一个进行加载，后续中间件存在依赖关系
				service.Middleware().CORS,            // 跨域中间件，自动处理跨域问题
				service.Middleware().PreFilter,       // 请求输入预处理，api使用gf规范路由并且XxxReq结构体实现了validate.Filter接口即可隐式预处理
				service.Middleware().ResponseHandler, // HTTP响应预处理，在业务处理完成后，对响应结果进行格式化和错误过滤，将处理后的数据发送给请求方
			}...)

			s.Group("/api/goview", func(group *ghttp.RouterGroup) {
				group.Bind(
					site.NewV1(),
				)
				group.Middleware(service.Middleware().AdminAuth)
				group.Bind(
					project.NewV1(),
				)
			})
			s.Run()
			return nil
		},
	}
)
