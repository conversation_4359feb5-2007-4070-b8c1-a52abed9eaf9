// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminProject is the golang structure for table admin_project.
type AdminProject struct {
	Id          int64       `json:"id"          description:"项目id"`
	ProjectName string      `json:"projectName" description:"项目名称"`
	IndexImage  string      `json:"indexImage"  description:"预览图片url"`
	Content     string      `json:"content"     description:"项目数据"`
	Status      int         `json:"status"      description:"项目状态,-1: 未发布'1: 已发布"`
	Remarks     string      `json:"remarks"     description:"项目备注"`
	CreatedBy   int64       `json:"createdBy"   description:"创建人"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   description:"更新时间"`
	CreatedAt   *gtime.Time `json:"createdAt"   description:"创建时间"`
	DeletedAt   *gtime.Time `json:"deletedAt"   description:"删除时间"`
}
