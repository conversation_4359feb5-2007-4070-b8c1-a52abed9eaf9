// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminMember is the golang structure of table hg_admin_member for DAO operations like Where/Data.
type AdminMember struct {
	g.Meta       `orm:"table:hg_admin_member, do:true"`
	Id           interface{} // 管理员ID
	Nickname     interface{} // 昵称
	Username     interface{} // 帐号
	PasswordHash interface{} // 密码
	Salt         interface{} // 密码盐
	Avatar       interface{} // 头像
	LastActiveAt *gtime.Time // 最后活跃时间
	Remark       interface{} // 备注
	Status       interface{} // 状态
	CreatedAt    *gtime.Time // 创建时间
	UpdatedAt    *gtime.Time // 修改时间
}
