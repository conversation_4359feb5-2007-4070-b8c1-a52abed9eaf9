package project

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"hotgo/internal/library/response"
	"hotgo/internal/service"

	"hotgo/api/project/v1"
)

func (c *ControllerV1) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	list, count, err := service.AdminProject().List(ctx, &req.ProjectListInp)
	if err != nil {
		return nil, err
	}

	response.CustomJson(ghttp.RequestFromCtx(ctx), g.Map{
		"code":  200,
		"count": count,
		"data":  list,
		"msg":   "获取成功",
	})
	return
}
