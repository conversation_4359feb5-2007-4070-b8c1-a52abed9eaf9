// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package project

import (
	"context"

	"hotgo/api/project/v1"
)

type IProjectV1 interface {
	List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error)
	GetData(ctx context.Context, req *v1.GetDataReq) (res *v1.GetDataRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Edit(ctx context.Context, req *v1.EditReq) (res *v1.EditRes, err error)
	SaveData(ctx context.Context, req *v1.SaveDataReq) (res *v1.SaveDataRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	Publish(ctx context.Context, req *v1.PublishReq) (res *v1.PublishRes, err error)
	Upload(ctx context.Context, req *v1.UploadReq) (res *v1.UploadRes, err error)
}
