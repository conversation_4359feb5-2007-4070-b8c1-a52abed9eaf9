// Package adminin
// @Description
// <AUTHOR> <<EMAIL>>
package adminin

import (
	"context"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"hotgo/internal/model/entity"
	"hotgo/internal/model/input/form"
)

// ProjectListInp 查询列表
type ProjectListInp struct {
	form.PageReq
}

type ProjectListModel struct {
	entity.AdminProject
	State        int         `json:"state"`
	CreateTime   *gtime.Time `json:"createTime"`
	CreateUserId int64       `json:"createUserId"`
}

// ProjectCreateInp 新增项目
type ProjectCreateInp struct {
	ProjectName string `json:"projectName"`
	Remarks     string `json:"remarks"`
	IndexImage  string `json:"indexImage"`
}

type ProjectCreateModel struct {
	Id           int64       `json:"id"`
	ProjectName  string      `json:"projectName"`
	State        int         `json:"state"`
	CreateTime   *gtime.Time `json:"createTime"`
	CreateUserId int64       `json:"createUserId"`
	IndexImage   string      `json:"indexImage"`
	Remarks      string      `json:"remarks"`
}

// ProjectEditInp 修改项目数据
type ProjectEditInp struct {
	entity.AdminProject
}

type ProjectEditModel struct{}

func (in *ProjectEditInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}
	return
}

// ProjectSaveDataInp 保存项目数据
type ProjectSaveDataInp struct {
	Id      int64  `json:"projectId"`
	Content string `json:"content"     description:"项目数据"`
}

type ProjectSaveDataModel struct{}

func (in *ProjectSaveDataInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}
	return
}

// ProjectDeleteInp 删除项目类型
type ProjectDeleteInp struct {
	Id int64 `json:"id" v:"required#项目ID不能为空" dc:"项目ID"`
}

type ProjectDeleteModel struct{}

// ProjectGetDataInp 获取信息
type ProjectGetDataInp struct {
	Id int64 `json:"projectId" dc:"项目ID"`
}

func (in *ProjectGetDataInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}
	return
}

type ProjectGetDataModel struct {
	Id           int64       `json:"id"`
	ProjectName  string      `json:"projectName"`
	State        int         `json:"state"`
	CreateTime   *gtime.Time `json:"createTime"`
	CreateUserId int64       `json:"createUserId"`
	IndexImage   string      `json:"indexImage"`
	Remarks      string      `json:"remarks"`
	Content      string      `json:"content"     description:"项目数据"`
	Status       int         `json:"status"`
}

// ProjectPublishInp 修改发布状态
type ProjectPublishInp struct {
	Id          int64  `json:"id"`
	ProjectName string `json:"projectName"`
	State       int    `json:"state"`
}

func (in *ProjectPublishInp) Filter(ctx context.Context) (err error) {
	if in.Id <= 0 {
		err = gerror.New("ID不能为空")
		return
	}
	return
}

type ProjectPublishModel struct{}

// ProjectUploadInp 文件上传
type ProjectUploadInp struct {
	File *ghttp.UploadFile `json:"object" type:"file" dc:"文件"`
}
