package adminin

import (
	"context"
	"github.com/gogf/gf/v2/os/gtime"
)

// RegisterInp 账号注册
type RegisterInp struct {
	Username string `json:"username" v:"required#用户名不能为空" dc:"用户名"`
	Password string `json:"password" v:"required#密码不能为空" dc:"密码"`
	Nickname string `json:"nickname" dc:"昵称"`
}

func (in *RegisterInp) Filter(ctx context.Context) (err error) {
	return
}

type LoginToken struct {
	TokenValue string `json:"tokenValue"`
	TokenName  string `json:"tokenName"`
	Expires    int64  `json:"expires"`
}

type LoginUserInfo struct {
	Id       int64  `json:"id"              dc:"用户ID"`
	Username string `json:"username"        dc:"用户名"`
	Nickname string `json:"nickname"        dc:"昵称"`
}

// LoginModel 统一登录响应
type LoginModel struct {
	UserInfo *LoginUserInfo `json:"userinfo"     dc:"用户信息"`
	Token    *LoginToken    `json:"token"        dc:"登录token"`
}

// AccountLoginInp 账号登录
type AccountLoginInp struct {
	Username string `json:"username" v:"required#用户名不能为空" dc:"用户名"`
	Password string `json:"password" v:"required#密码不能为空" dc:"密码"`
}

// MobileLoginInp 手机号登录
type MobileLoginInp struct {
	Mobile string `json:"mobile" v:"required|phone-loose#手机号不能为空|手机号格式不正确" dc:"手机号"`
	Code   string `json:"code" v:"required#验证码不能为空"  dc:"验证码"`
}

// MemberLoginPermissions 登录用户角色信息
type MemberLoginPermissions []string

// MemberLoginStatInp 用户登录统计
type MemberLoginStatInp struct {
	MemberId int64
}

type MemberLoginStatModel struct {
	LoginCount  int         `json:"loginCount"  dc:"登录次数"`
	LastLoginAt *gtime.Time `json:"lastLoginAt" dc:"最后登录时间"`
	LastLoginIp string      `json:"lastLoginIp" dc:"最后登录IP"`
}
