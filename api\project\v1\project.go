// Package v1
// @Description
// <AUTHOR> <<EMAIL>>
package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"hotgo/internal/model/input/adminin"
	"hotgo/internal/model/input/form"
)

// ListReq 查询列表
type ListReq struct {
	g.Meta `path:"/project/list" method:"get" tags:"项目" summary:"获取项目列表"`
	adminin.ProjectListInp
}

type ListRes struct {
	g.Meta `mime:"custom"`
	List   []*adminin.ProjectListModel `json:"list"   dc:"数据列表"`
	form.PageRes
}

// GetDataReq 获取信息
type GetDataReq struct {
	g.Meta `path:"/project/getData" method:"get" tags:"项目" summary:"获取指定信息"`
	adminin.ProjectGetDataInp
}

type GetDataRes struct {
	*adminin.ProjectGetDataModel
}

// CreateReq 新增项目
type CreateReq struct {
	g.Meta `path:"/project/create" method:"post" tags:"项目" summary:"新增项目"`
	adminin.ProjectCreateInp
}

type CreateRes struct {
	*adminin.ProjectCreateModel
}

// EditReq 修改/新增
type EditReq struct {
	g.Meta `path:"/project/edit" method:"post" tags:"项目" summary:"修改/新增项目"`
	adminin.ProjectEditInp
}

type EditRes struct{}

// SaveDataReq 保存项目数据
type SaveDataReq struct {
	g.Meta `path:"/project/save/data" method:"post" tags:"项目" summary:"保存项目数据"`
	adminin.ProjectSaveDataInp
}

type SaveDataRes struct{}

// DeleteReq 删除
type DeleteReq struct {
	g.Meta `path:"/project/delete" method:"delete" tags:"项目" summary:"删除项目"`
	adminin.ProjectDeleteInp
}

type DeleteRes struct{}

// PublishReq 修改发布状态
type PublishReq struct {
	g.Meta `path:"/project/publish" method:"put" tags:"项目" summary:"修改发布状态"`
	adminin.ProjectPublishInp
}

type PublishRes struct{}

// UploadReq 文件上传
type UploadReq struct {
	g.Meta `path:"/project/upload" method:"post" tags:"项目" summary:"文件上传"`
	adminin.ProjectUploadInp
}

type UploadRes struct {
	FileUrl string `json:"fileurl"`
}
